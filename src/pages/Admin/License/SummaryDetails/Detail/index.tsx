import { Button, Form, Input, Select, message } from 'antd';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { useState } from 'react';
import {
  getLicenseSummaryDetailForm,
  putLicenseSummaryDetailForm,
} from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';

interface batchNumbers {
  id: number;
  productionOrderNumber: string;
}

function SummaryDetail() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const { id } = useParams();
  const [batchNumbers, setBatchNumbers] = useState<batchNumbers[]>([]);
  const { isLoading } = useQuery(
    ['summaryDetailForm', id],
    () => getLicenseSummaryDetailForm(id || ''),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        form.setFieldsValue(data);
        setBatchNumbers(data.batchNumbers || []);
      },
    }
  );
  useSetGlobalLoading(isLoading);
  const onFinish = async (values: any) => {
    console.log(values);
    const params = {
      remaining: values.remaining,
      comments: values.comments,
      id: id || '',
      productionOrderNumber: values.productionOrderNumber,
    };
    const data = await putLicenseSummaryDetailForm(params);
    if (data) {
      message.success('Updated successfully', 1).then(() => {
        router(-1);
      });
    }
  };
  return (
    <FormCom pageTitle="License summary details">
      <Form
        style={{ width: '600px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
      >
        <Form.Item
          label="Available Licenses"
          name="remaining"
          rules={[
            {
              required: true,
              pattern: /^[0-9]*$/g,
              message: 'Please input number',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item label="Company" name="company">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Mode" name="mode">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Speed type" name="speedType">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Feature type" name="featureType">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Expiration date" name="expirationDate">
          <Input disabled />
        </Form.Item>
        <Form.Item
          label="Production Order Number"
          name="productionOrderNumber"
          rules={[
            {
              required: true,
              message: 'Please select production order number',
            },
          ]}
        >
          <Select
            placeholder="Select production order number"
            showSearch
            options={batchNumbers.map((item) => ({
              label: item.productionOrderNumber,
              value: item.id,
            }))}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
          />
        </Form.Item>
        <Form.Item label="Assigned Licenses" name="assigned">
          <Input disabled />
        </Form.Item>
        <Form.Item label="Comment" name="comments">
          <Input.TextArea />
        </Form.Item>
        <Form.Item
          wrapperCol={{
            offset: 18,
          }}
        >
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default SummaryDetail;

import { Button, DatePicker, Form, Input, Select, message } from 'antd';
import { useQuery } from 'react-query';
import { useNavigate, useParams } from 'react-router-dom';
import {
  getAdminLicenseAddForm,
  getAdminLicenseDetail,
  getExpirationDate,
  postAdminLicense,
  putAdminLicense,
  putAdminLicenseKey,
} from '@/api/admin-license';
import useSetGlobalLoading from '@/hooks/useSetGlobalLoading';
import FormCom from '@/pages/Licenses/components/FormCom';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';

export type moBatchNumberOperation = Root2[];

export interface Root2 {
  expirationDate: string;
  moBatchNumbers: MoBatchNumber[];
  summaryId: number;
}

export interface MoBatchNumber {
  id: number;
  productionOrderNumber: string;
}

function AdminLicensesDetail() {
  const router = useNavigate();
  const [form] = Form.useForm();
  const modeValue = Form.useWatch('mode', form);
  const statusField = Form.useWatch('status', form);
  const orgInfo = Form.useWatch('organizationalInformation', form);
  const expirationDate = Form.useWatch('expirationDate', form);
  const { id } = useParams();
  const speedType = useRef('');
  const licenseKeyParams = useRef({} as any);
  const fetchParams = useRef({} as any);
  const [expirationDateList, setExpirationDateList] = useState<string[]>([]);
  const [moBatchNumberOperation, setMoBatchNumberOperation] =
    useState<moBatchNumberOperation>([]);
  const { data: optionData, isLoading } = useQuery(
    ['adminLicensesForm'],
    () => getAdminLicenseAddForm(),
    {
      select: (res) => res.data,
      onSuccess: (data) => {
        setExpirationDateList(data.expirationDateList);
      },
    }
  );
  useQuery(['AdminLicensesDetail', id], () => getAdminLicenseDetail(id || ''), {
    enabled: !!id && !!optionData,
    select: (res) => res.data,
    onSuccess: (data) => {
      console.log(data);
      speedType.current = data.speedType;
      if (id) {
        data.expirationDate = dayjs(data.expirationDate);
      }
      form.setFieldsValue(data);
    },
  });

  useSetGlobalLoading(isLoading);

  const onFinish = async (value: any) => {
    if (id) {
      value.expirationDate = dayjs(value.expirationDate).format('YYYY-MM-DD');
      const { data } = await putAdminLicense({ ...value, id });
      console.log(data);
      if (data.result) {
        message.success('Updated successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(data.message, 1);
      }
    } else {
      const { data } = await postAdminLicense({ ...value });
      if (data.result) {
        message.success('Created successfully', 1).then(() => {
          router(-1);
        });
      } else {
        message.error(data.message, 1);
      }
    }
  };
  const fetchExpirationDate = async (changedValues: any) => {
    const fieldList = ['companyId', 'mode', 'speedTypeId', 'featureTypeId'];
    const isChange = fieldList.some((item) => {
      return changedValues[item];
    });
    if (!isChange) {
      return;
    }
    //当变化的字段在fieldList中时，就将字段和值存储到一个对象中
    fieldList.forEach((item) => {
      if (changedValues[item]) {
        fetchParams.current[item] = changedValues[item];
      }
    });
    const data = await getExpirationDate({
      ...fetchParams.current,
      timeFilter: 1,
    });
    form.setFieldValue('expirationDate', '');
    form.setFieldValue('productionOrderNumberId', '');
    setExpirationDateList(data.data.expirationDateList);
    setMoBatchNumberOperation(data.data.moBatchNumberOperation || []);
  };

  const onValueChange = async (changedValues: any) => {
    if (!id) {
      fetchExpirationDate(changedValues);
      return;
    }
    const validateFieldsData = await form
      .validateFields({ dirty: true })
      .catch((error) => error);
    if (validateFieldsData.errorFields?.length) {
      return;
    }
    const fieldList = ['expirationDate', 'hardwareId', 'featureTypeId'];
    const isChange = fieldList.some((item) => {
      return changedValues[item];
    });
    if (!isChange) {
      return;
    }
    //当变化的字段在fieldList中时，就将字段和值存储到一个对象中
    fieldList.forEach((item) => {
      if (changedValues[item]) {
        licenseKeyParams.current[item] = changedValues[item];
      } else {
        licenseKeyParams.current[item] = form.getFieldValue(item);
      }
      if (item === 'expirationDate') {
        licenseKeyParams.current[item] = changedValues[item]
          ? changedValues[item].format('YYYY-MM-DD')
          : licenseKeyParams.current[item].format('YYYY-MM-DD');
      }
    });
    licenseKeyParams.current.id = id;
    licenseKeyParams.current.speedType = speedType.current;
    const data = await putAdminLicenseKey(licenseKeyParams.current);
    if (data?.data?.result) {
      form.setFieldValue('licenseKey', data.data.message);
    }
  };

  // 根据选中的Expiration date获取对应的MO Batch Numbers
  const getMoBatchNumbers = (): MoBatchNumber[] => {
    if (!expirationDate || !moBatchNumberOperation.length) {
      return [];
    }

    const selectedDateStr =
      typeof expirationDate === 'string'
        ? expirationDate
        : dayjs(expirationDate).format('YYYY-MM-DD');

    const matchedOperation = moBatchNumberOperation.find(
      (operation) => operation.expirationDate === selectedDateStr
    );

    return matchedOperation?.moBatchNumbers || [];
  };

  useEffect(() => {
    if (modeValue === 'switch') {
      form.setFieldsValue({ siteName: '' });
    } else if (modeValue === 'site') {
      form.setFieldsValue({ hardwareId: '' });
    }
  }, [modeValue]);

  // 当Expiration date变化时，清空Production Order Number的值
  useEffect(() => {
    form.setFieldValue('productionOrderNumberId', '');
  }, [expirationDate, form]);
  return (
    <FormCom pageTitle="Admin panel">
      <Form
        style={{ width: '750px' }}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 14 }}
        form={form}
        onFinish={onFinish}
        onValuesChange={onValueChange}
        disabled={statusField === 'D'}
      >
        <Form.Item
          label="Company"
          name="companyId"
          rules={[{ required: true, message: 'Please select company' }]}
        >
          <Select
            options={optionData?.companyList || []}
            fieldNames={{ label: 'name', value: 'id' }}
            disabled={!!id}
            showSearch
            filterOption={(input, option) =>
              (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
            }
          ></Select>
        </Form.Item>
        <Form.Item
          label="Mode"
          name="mode"
          rules={[{ required: true, message: 'Please select mode' }]}
          initialValue={'switch'}
        >
          <Select disabled={!!id}>
            {optionData?.modeList?.map((item: any) => (
              <Select.Option key={item} value={item}>
                {item}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label="Speed type"
          name="speedTypeId"
          rules={[{ required: true, message: 'Please select speed type' }]}
        >
          <Select
            disabled={!!id}
            options={optionData?.speedTypeList || []}
            fieldNames={{ label: 'name', value: 'id' }}
          ></Select>
        </Form.Item>
        <Form.Item
          label="Feature type"
          name="featureTypeId"
          rules={[{ required: true, message: 'Please select feature type' }]}
        >
          <Select
            disabled={!!id}
            options={optionData?.featureTypeList || []}
            fieldNames={{ label: 'name', value: 'id' }}
          ></Select>
        </Form.Item>
        <Form.Item
          label="Site name"
          name="siteName"
          rules={[
            {
              required: modeValue === 'site',
              message: 'Please, provide site name',
            },
          ]}
        >
          <Input disabled={modeValue === 'switch'} />
        </Form.Item>
        <Form.Item
          label="Hardware ID"
          name="hardwareId"
          extra="Hardware ID format ZZZZ-ZZZZ-ZZZZ-ZZZZ"
          rules={[
            {
              required: modeValue === 'switch',
              message: 'Please, provide correct format',
              pattern: /^[0-9a-fA-F]{4}(-[0-9a-fA-F]{4}){3}$/,
            },
          ]}
        >
          <Input disabled={statusField === 'D' || modeValue === 'site'} />
        </Form.Item>
        <Form.Item
          label="Serial Number"
          name="serialNumber"
          extra="Serial Number format Z0123456789"
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="Organizational Information"
          name="organizationalInformation"
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="FS Order Number"
          name="fsOrderNumber"
          rules={[
            {
              required: !!orgInfo,
              message: 'Please input FS Order Number',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="Purchase Order Number"
          name="purchaseOrderNumber"
          rules={[
            {
              message: 'Please input Purchase Order Number',
            },
          ]}
        >
          <Input />
        </Form.Item>
        <Form.Item
          label="Expiration date"
          name="expirationDate"
          rules={[{ required: true, message: 'Please select expiration date' }]}
        >
          {id ? (
            <DatePicker />
          ) : (
            <Select>
              {expirationDateList.map((v: string, index: number) => (
                <Select.Option key={index} value={v}>
                  {v}
                </Select.Option>
              ))}
            </Select>
          )}
        </Form.Item>
        {id && (
          <Form.Item wrapperCol={{ offset: 8 }}>
            <div style={{ color: '#999', fontSize: '12px' }}>
              Please use the dropdown to check the support expiration dates
              available and select the correct one from the list; once the
              license is assigned to a switch, this selection cannot be changed!
            </div>
          </Form.Item>
        )}
        {expirationDate && (
          <Form.Item
            label="Production Order Number"
            name="productionOrderNumberId"
          >
            <Select
              placeholder="Select production order number (optional)"
              allowClear
              options={getMoBatchNumbers().map((item) => ({
                label: item.productionOrderNumber,
                value: item.id,
              }))}
            />
          </Form.Item>
        )}
        <Form.Item label="License name" name="name">
          <Input />
        </Form.Item>
        {!!id && (
          <Form.Item
            label="License key"
            name="licenseKey"
            wrapperCol={{ span: 20 }}
          >
            <Input.TextArea
              disabled
              style={{ height: '150px', fontSize: '12px' }}
            />
          </Form.Item>
        )}
        <Form.Item
          label="Comment"
          name="comments"
          // rules={[{ required: true, message: 'Please input comment' }]}
        >
          <Input.TextArea maxLength={255} />
        </Form.Item>
        {!!id && (
          <Form.Item label="Status" name="status">
            <Input disabled />
          </Form.Item>
        )}
        <Form.Item wrapperCol={{ offset: 8, span: 14 }}>
          <Button type="primary" htmlType="submit">
            Save
          </Button>
        </Form.Item>
      </Form>
    </FormCom>
  );
}

export default AdminLicensesDetail;
